import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { Textarea } from "../../components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select";
import { Badge } from "../../components/ui/badge";
import { Switch } from "../../components/ui/switch";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  FileText,
  DollarSign,
  Star,
  Globe,
  Eye,
  Download
} from "lucide-react";
import { apiRequest } from "../../lib/queryClient";

interface Guide {
  id: number;
  title: string;
  titleEs: string;
  description: string;
  descriptionEs: string;
  formType: string;
  price: string;
  skillLevel: string;
  featured: boolean;
  onlineFiling: boolean;
  fileName?: string;
  fileContent?: string;
  fileType?: string;
  createdAt: string;
}

export function AdminProducts() {
  const [guides, setGuides] = useState<Guide[]>([]);
  const [filteredGuides, setFilteredGuides] = useState<Guide[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingGuide, setEditingGuide] = useState<Guide | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showFilePreview, setShowFilePreview] = useState(false);
  const [previewGuide, setPreviewGuide] = useState<Guide | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    titleEs: "",
    description: "",
    descriptionEs: "",
    formType: "",
    price: "",
    skillLevel: "beginner",
    featured: false,
    onlineFiling: false
  });

  useEffect(() => {
    fetchGuides();
  }, []);

  useEffect(() => {
    const filtered = guides.filter(guide =>
      guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.formType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.skillLevel.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredGuides(filtered);
  }, [guides, searchTerm]);

  const fetchGuides = async () => {
    try {
      const response = await apiRequest("GET", "/api/admin/guides");
      const data = await response.json();
      setGuides(data);
    } catch (error) {
      console.error("Failed to fetch guides:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (editingGuide) {
        const response = await apiRequest("PUT", `/api/admin/guides/${editingGuide.id}`, formData);
        const updatedGuide = await response.json();
        setGuides(guides.map(g => g.id === editingGuide.id ? updatedGuide : g));

        // If there's a file selected for editing, upload it
        if (selectedFile) {
          await handleFileUpload(editingGuide.id);
        }
      } else {
        // First create the guide
        const response = await apiRequest("POST", "/api/admin/guides", formData);
        const newGuide = await response.json();

        // If there's a file selected, upload it
        if (selectedFile && newGuide.id) {
          await handleFileUpload(newGuide.id);
        }

        setGuides([...guides, newGuide]);
      }

      resetForm();
      setShowModal(false);
    } catch (error) {
      console.error("Failed to save guide:", error);
    }
  };

  const handleFileUpload = async (guideId: number) => {
    if (!selectedFile) return;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch(`/api/admin/guides/${guideId}/upload-file`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('File upload failed');
      }

      // Refresh the guides list to show updated file info
      await fetchGuides();
    } catch (error) {
      console.error("Failed to upload file:", error);
    }
  };

  const handleDownloadFile = (guide: Guide) => {
    if (!guide.fileContent || !guide.fileName) {
      console.error("No file content available");
      return;
    }

    try {
      // Convert base64 to blob
      const byteCharacters = atob(guide.fileContent);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: guide.fileType || 'application/octet-stream' });

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = guide.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Failed to download file:", error);
    }
  };

  const handleViewFile = (guide: Guide) => {
    if (!guide.fileContent || !guide.fileName) {
      console.error("No file content available");
      return;
    }

    setPreviewGuide(guide);
    setShowFilePreview(true);
  };

  const handleViewFileInNewTab = (guide: Guide) => {
    if (!guide.fileContent || !guide.fileName) {
      console.error("No file content available");
      return;
    }

    try {
      // Convert base64 to blob
      const byteCharacters = atob(guide.fileContent);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: guide.fileType || 'application/octet-stream' });

      // Create URL and open in new tab
      const url = window.URL.createObjectURL(blob);
      window.open(url, '_blank');

      // Clean up the URL after a delay to allow the browser to load it
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
      }, 1000);
    } catch (error) {
      console.error("Failed to view file:", error);
    }
  };

  const deleteGuide = async (id: number) => {
    if (!confirm("Are you sure you want to delete this guide?")) return;

    try {
      await apiRequest("DELETE", `/api/admin/guides/${id}`);
      setGuides(guides.filter(g => g.id !== id));
    } catch (error) {
      console.error("Failed to delete guide:", error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: "",
      titleEs: "",
      description: "",
      descriptionEs: "",
      formType: "",
      price: "",
      skillLevel: "beginner",
      featured: false,
      onlineFiling: false
    });
    setEditingGuide(null);
    setSelectedFile(null);
  };

  const openEditModal = (guide: Guide) => {
    setEditingGuide(guide);
    setFormData({
      title: guide.title,
      titleEs: guide.titleEs,
      description: guide.description,
      descriptionEs: guide.descriptionEs,
      formType: guide.formType,
      price: guide.price,
      skillLevel: guide.skillLevel,
      featured: guide.featured,
      onlineFiling: guide.onlineFiling
    });
    // Reset selected file when opening edit modal
    setSelectedFile(null);
    setShowModal(true);
  };

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Product Management</h1>
        <Button onClick={() => setShowModal(true)} className="flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Add New Guide</span>
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search guides..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Guides Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredGuides.map((guide) => (
          <Card key={guide.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-2">{guide.title}</CardTitle>
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge variant="outline">{guide.formType}</Badge>
                    <Badge className={getSkillLevelColor(guide.skillLevel)}>
                      {guide.skillLevel}
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {guide.featured && <Star className="w-4 h-4 text-yellow-500" />}
                  {guide.onlineFiling && <Globe className="w-4 h-4 text-blue-500" />}
                  {guide.fileName && <FileText className="w-4 h-4 text-green-500" />}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4 line-clamp-3">{guide.description}</p>

              {guide.fileName ? (
                <div className="flex items-center justify-between mb-3 p-2 bg-green-50 rounded-md border border-green-200">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <FileText className="w-4 h-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-green-700 truncate">{guide.fileName}</span>
                  </div>
                  <div className="flex space-x-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewFile(guide);
                      }}
                      className="h-6 px-2 text-green-600 hover:text-green-700 hover:bg-green-100"
                      title="View file"
                    >
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDownloadFile(guide);
                      }}
                      className="h-6 px-2 text-green-600 hover:text-green-700 hover:bg-green-100"
                      title="Download file"
                    >
                      <Download className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2 mb-3 p-2 bg-gray-50 rounded-md border border-gray-200">
                  <FileText className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-500">No file attached</span>
                </div>
              )}

              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-1">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  <span className="font-bold text-lg">{guide.price}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(guide.createdAt).toLocaleDateString()}
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => openEditModal(guide)}
                  className="flex-1"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => deleteGuide(guide.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold">
                {editingGuide ? 'Edit Guide' : 'Add New Guide'}
              </h3>
              <Button variant="outline" onClick={() => {
                setShowModal(false);
                resetForm();
              }}>
                ×
              </Button>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title (English)</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="titleEs">Title (Spanish)</Label>
                  <Input
                    id="titleEs"
                    value={formData.titleEs}
                    onChange={(e) => setFormData({...formData, titleEs: e.target.value})}
                    required
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="description">Description (English)</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="descriptionEs">Description (Spanish)</Label>
                <Textarea
                  id="descriptionEs"
                  value={formData.descriptionEs}
                  onChange={(e) => setFormData({...formData, descriptionEs: e.target.value})}
                  rows={3}
                  required
                />
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="formType">Form Type</Label>
                  <Input
                    id="formType"
                    value={formData.formType}
                    onChange={(e) => setFormData({...formData, formType: e.target.value})}
                    placeholder="e.g., I-130"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="price">Price</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => setFormData({...formData, price: e.target.value})}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="skillLevel">Skill Level</Label>
                  <Select value={formData.skillLevel} onValueChange={(value) => setFormData({...formData, skillLevel: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) => setFormData({...formData, featured: checked})}
                  />
                  <Label htmlFor="featured">Featured Guide</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="onlineFiling"
                    checked={formData.onlineFiling}
                    onCheckedChange={(checked) => setFormData({...formData, onlineFiling: checked})}
                  />
                  <Label htmlFor="onlineFiling">Online Filing Available</Label>
                </div>
              </div>

              {/* File Upload Section */}
              <div>
                <Label htmlFor="guideFile">Guide File (PDF, DOC, DOCX)</Label>
                <div className="mt-2 space-y-3">
                  {/* Current File Status */}
                  {editingGuide && (
                    <div className="p-3 bg-gray-50 rounded-md border">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-700">Current File Status:</span>
                        </div>
                      </div>
                      {editingGuide.fileName ? (
                        <div className="mt-2 space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="flex items-center space-x-2 text-green-700 bg-green-100 px-2 py-1 rounded">
                                <FileText className="w-4 h-4" />
                                <span className="text-sm font-medium">File Attached</span>
                              </div>
                              <span className="text-sm text-gray-600">{editingGuide.fileName}</span>
                            </div>
                            {editingGuide.fileContent && (
                              <div className="flex space-x-2">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleViewFile(editingGuide)}
                                  className="text-xs flex items-center space-x-1"
                                >
                                  <Eye className="w-3 h-3" />
                                  <span>View</span>
                                </Button>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDownloadFile(editingGuide)}
                                  className="text-xs flex items-center space-x-1"
                                >
                                  <Download className="w-3 h-3" />
                                  <span>Download</span>
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="mt-2 flex items-center space-x-2">
                          <div className="flex items-center space-x-2 text-orange-700 bg-orange-100 px-2 py-1 rounded">
                            <FileText className="w-4 h-4" />
                            <span className="text-sm font-medium">No File Attached</span>
                          </div>
                          <span className="text-sm text-gray-500">No file has been uploaded for this guide</span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* File Upload Input */}
                  <div>
                    <input
                      id="guideFile"
                      type="file"
                      accept=".pdf,.doc,.docx"
                      onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                    {selectedFile && (
                      <div className="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                        <div className="flex items-center space-x-2">
                          <FileText className="w-4 h-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-800">New file selected:</span>
                        </div>
                        <p className="text-sm text-blue-700 mt-1">
                          {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                        </p>
                        {editingGuide && editingGuide.fileName && (
                          <p className="text-xs text-blue-600 mt-1">
                            This will replace the current file when you save.
                          </p>
                        )}
                      </div>
                    )}
                    {!editingGuide && (
                      <p className="text-xs text-gray-500 mt-1">
                        Optional: Upload a guide file that users can download after purchase.
                      </p>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2 pt-4">
                <Button type="submit" className="flex-1">
                  {editingGuide ? 'Update Guide' : 'Create Guide'}
                </Button>
                <Button type="button" variant="outline" onClick={() => {
                  setShowModal(false);
                  resetForm();
                }}>
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* File Preview Modal */}
      {showFilePreview && previewGuide && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-xl font-bold">File Preview</h3>
                <p className="text-gray-600">{previewGuide.fileName}</p>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => handleViewFileInNewTab(previewGuide)}
                  className="flex items-center space-x-2"
                >
                  <Eye className="w-4 h-4" />
                  <span>Open in New Tab</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleDownloadFile(previewGuide)}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowFilePreview(false);
                    setPreviewGuide(null);
                  }}
                >
                  ×
                </Button>
              </div>
            </div>

            <div className="flex-1 overflow-hidden">
              {previewGuide.fileType?.includes('pdf') ? (
                <iframe
                  src={`data:${previewGuide.fileType};base64,${previewGuide.fileContent}`}
                  className="w-full h-full border rounded"
                  title="PDF Preview"
                />
              ) : (
                <div className="flex flex-col items-center justify-center h-full bg-gray-50 rounded border">
                  <FileText className="w-16 h-16 text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-2">Preview not available for this file type</p>
                  <p className="text-sm text-gray-500 mb-4">
                    File type: {previewGuide.fileType || 'Unknown'}
                  </p>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handleViewFileInNewTab(previewGuide)}
                      className="flex items-center space-x-2"
                    >
                      <Eye className="w-4 h-4" />
                      <span>Open in New Tab</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleDownloadFile(previewGuide)}
                      className="flex items-center space-x-2"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}